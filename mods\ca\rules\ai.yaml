Player:
	ModularB<PERSON>@BrutalAI:
		Name: bot-brutal-ai.name
		Type: brutal
	ModularBot@VeryHardAI:
		Name: bot-vhard-ai.name
		Type: vhard
	ModularBot@HardAI:
		Name: bot-hard-ai.name
		Type: hard
	ModularBot@NormalAI:
		Name: bot-normal-ai.name
		Type: normal
	ModularBot@EasyAI:
		Name: bot-easy-ai.name
		Type: easy
	ModularBot@NavalAI:
		Name: bot-naval-ai.name
		Type: naval
	ModularBot@SeriousAI:
		Name: bot-serious-ai.name
		Type: serious
	GrantConditionOnBotOwner@BrutalAI:
		Condition: enable-brutal-ai
		Bots: brutal
	GrantConditionOnBotOwner@VeryHardAI:
		Condition: enable-vhard-ai
		Bots: vhard
	GrantConditionOnBotOwner@HardAI:
		Condition: enable-hard-ai
		Bots: hard
	GrantConditionOnBotOwner@NormalAI:
		Condition: enable-normal-ai
		Bots: normal
	GrantConditionOnBotOwner@EasyAI:
		Condition: enable-easy-ai
		Bots: easy
	GrantConditionOnBotOwner@NavalAI:
		Condition: enable-naval-ai
		Bots: naval
	GrantConditionOnBotOwner@SeriousAI:
		Condition: enable-serious-ai
		Bots: serious
	# Serious AI Configuration - Enhanced AI with wall building and defense placement
	# Serious AI Configuration - Enhanced AI with wall building and defense placement
	SeriousDefensePlacementModule:
		RequiresCondition: enable-serious-ai
		DefenseTypes: pbox, gun, atwr, sam, agun, obli, gtwr, nuke, tmpl, ftur, ptur, ltur
		MinDistanceFromMapEdge: 5
		MaxDistanceFromWalls: 3
		CheckInterval: 250
	SeriousWallBuilderModule:
		RequiresCondition: enable-serious-ai
		WallTypes: sbag, fenc, chain, brik
		MinWallDistance: 8
		MaxWallDistance: 12
		GapCount: 2
		GapSize: 2
	BaseBuilderBotModuleCA@serious:
		RequiresCondition: enable-serious-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 150
		ExcessPowerIncrement: 60
		ExcessPowerIncreaseThreshold: 6
		PlaceDefenseTowardsEnemyChance: 50
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		MinBaseRadius: 1
		MaxBaseRadius: 8
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			weap: 4
			weap.td: 4
			airs: 4
			wsph: 4
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 5
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 2
			pyle: 2
			barr: 2
			tent: 2
			port: 2
			weap: 2
			weap.td: 2
			airs: 2
			wsph: 2
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 7
			scol: 5
			tsla: 5
			obli: 5
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 3
			atwr: 7
			stwr: 5
			pris: 5
			agun: 2
			cram: 2
			sam: 2
			nsam: 2
			shar: 2
			afld: 10
			afld.gdi: 10
			grav: 3
	SquadManagerBotModuleCA@serious:
		RequiresCondition: enable-serious-ai
		SquadValue: 3000
		SquadValueRandomBonus: 1000
		MinimumAttackForceDelay: 35
		SquadSize: 10
		SquadSizeRandomBonus: 3
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,pt2,ss2,dd2,isub,sb,seas
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		AirToAirPriority: 20
		AirSquadTargetArmorTypes:
			mshp: Wood
	UnitBuilderBotModuleCA@serious:
		RequiresCondition: enable-serious-ai
		MaxAircraft: 1
		UnitDelays:
			arty: 8000
			arty.nod: 8000
			katy: 8000
			msam: 8000
		UnitIntervals:
			mshp: 7500
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
			rmbo: 5000
			e7: 5000
			mast: 5000
			yuri: 5000
			bori: 5000
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e6: 5
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n6: 5
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			avtr: 15
			mort.chem: 15
			medi: 3
			mech: 3
			dog: 2
			shok: 15
			ttrp: 15
			brut: 15
			e8: 15
			deso: 15
			snip: 15
			jjet: 15
			bjet: 15
			acol: 15
			tplr: 15
			bh: 15
			ztrp: 15
			zrai: 15
			zdef: 15
			enfo: 15
			hopl: 15
			tigr: 15
			cryt: 15
			evis: 15
			impl: 15
			stlk: 15
			ivan: 15
			cmsr: 15
			rmbo: 15
			e7: 15
			seal: 15
			bori: 15
			yuri: 15
			mast: 15
			apc: 20
			rapc: 20
			sapc: 10
			intl: 10
			jeep: 30
			apc2: 20
			vulc: 10
			hmmv: 30
			gdrn: 30
			mdrn: 20
			xo: 20
			wolv: 20
			pbul: 20
			jack: 10
			btr: 20
			btr.yuri: 20
			trpc: 10
			gunw: 20
			shrw: 20
			bggy: 30
			arty: 20
			howi: 20
			ptnk: 20
			pcan: 20
			v2rl: 20
			katy: 20
			grad: 20
			nukc: 20
			msam: 25
			mlrs: 20
			spec: 20
			hsam: 25
			ruin: 20
			atmz: 20
			1tnk: 70
			ifv.ai: 70
			2tnk: 45
			gtnk.squad: 20
			tnkd: 45
			rtnk: 45
			3tnk: 70
			3tnk.atomic: 70
			3tnk.yuri: 70
			3tnk.atomicyuri: 70
			3tnk.rhino: 70
			3tnk.rhino.atomic: 70
			3tnk.rhino.yuri: 70
			3tnk.rhino.atomicyuri: 70
			4tnk: 40
			4tnk.atomic: 40
			4tnk.erad: 40
			4tnk.erad.atomic: 40
			apoc: 30
			apoc.atomic: 30
			apoc.erad: 30
			apoc.erad.atomic: 30
			ovld: 30
			ovld.atomic: 30
			ovld.erad: 30
			ovld.erad.atomic: 30
			tpod: 40
			rtpd: 40
			ltnk: 40
			ltnk.laser: 40
			mtnk: 70
			mtnk.drone: 70
			mtnk.laser: 70
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			bike: 30
			htnk: 40
			htnk.ion: 40
			htnk.hover: 40
			htnk.drone: 40
			v3rl: 3
			thwk: 3
			zeus: 3
			titn: 40
			titn.rail: 40
			jugg: 10
			cryo: 10
			mgg: 3
			mrj: 3
			cdrn: 3
			nhaw: 3
			ctnk: 3
			chpr: 3
			batf.ai: 10
			wtnk: 3
			ttnk: 25
			ttra: 25
			isu: 20
			stnk.nod: 15
			hstk: 15
			ftnk: 15
			hftk: 15
			corr: 15
			lchr: 15
			stcr: 25
			oblt: 3
			null: 3
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 1
			msub: 1
			dd: 1
			ca: 1
			cv: 1
			pt: 1
			pt2: 1
			dd2: 1
			ss2: 2
			isub: 1
			sb: 1
			seas: 1
			stmr: 5
			torm: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
		UnitLimits:
			e2: 5
			n2: 8
			e4: 5
			n4: 5
			dog: 2
			cmsr: 1
			mgg: 1
			mrj: 1
			cdrn: 1
			nhaw: 1
			apc: 2
			apc2: 2
			rapc: 2
			vulc: 5
			hmmv: 2
			bggy: 2
			jeep: 2
			sapc: 1
			e6: 1
			n6: 1
			s6: 1
			u3.squad: 2
			seal: 5
			mech: 3
			medi: 3
			msub: 4
			ca: 4
			isub: 4
			cv: 4
			jjet: 8
			bjet: 8
			v3rl: 1
			nukc: 1
			thwk: 1
			zeus: 1
			oblt: 1
			kiro: 1
			disc: 1
			deva: 1
			pac: 1
			ss: 10
			ss2: 10
			sb: 10
			seas: 10
			harv: 8
			harv.td: 8
			harv.td.upg: 8
			harv.scrin: 8
			harv.chrono: 8
	SupportPowerBotModule:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-naval-ai || enable-serious-ai
		Decisions:
			crateairstrike:
				OrderName: crateairstrike
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			crateparabombs:
				OrderName: crateparabombs
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			spyplane:
				OrderName: spyplane
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			sathack:
				OrderName: sathack
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			sathacklegion:
				OrderName: sathacklegion
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			gdiuav:
				OrderName: gdiuav
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			gdiuavarc:
				OrderName: gdiuavarc
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			cashhack:
				OrderName: cashhack
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: ResourceDrainable
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
			substrike:
				OrderName: substrike
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			nodairdrop:
				OrderName: nodairdrop
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			blackhairdrop:
				OrderName: blackhairdrop
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			shadowairdrop:
				OrderName: shadowairdrop
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			eagledropzone:
				OrderName: dropzoneeagle
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			paratroopers:
				OrderName: paratroopers
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			rparatroopers:
				OrderName: stormtroopers
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			airborne:
				OrderName: airborne
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 8c0
				Consideration@2:
					Against: Ally
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 8c0
				Consideration@3:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 5c0
			airbornetank:
				OrderName: airbornetank
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 8c0
				Consideration@2:
					Against: Ally
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 8c0
				Consideration@3:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 5c0
			assassinsquadai:
				OrderName: assassinsquadai
				Consideration@1:
					Against: Ally
			hackercellai:
				OrderName: hackercellai
				Consideration@1:
					Against: Ally
			confessorcabalai:
				OrderName: confessorcabalai
				Consideration@1:
					Against: Ally
			droppods:
				OrderName: droppods
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			xodrop:
				OrderName: xodrop
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			tempinc:
				OrderName: tempinc
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			chronoshiftai:
				OrderName: Chronoshiftai
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			strafe:
				OrderName: strafe
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 4c0
			carpetbomb:
				OrderName: carpetbomb
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			gmutation:
				OrderName: mutabomb
				MinimumAttractiveness: 6
				Consideration@1:
					Against: Enemy
					Types: Infantry
					Attractiveness: 1
					CheckRadius: 4c0
			parabombs:
				OrderName: parabombs
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			iparabombs:
				OrderName: atombomb
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			infernobombs:
				OrderName: infernobomb
				MinimumAttractiveness: 1
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 5c0
			nukepower:
				OrderName: NukePowerInfoOrder
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, AirHighValue
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			chemmissilepower:
				OrderName: chemmissile
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, AirHighValue
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			clustermissilepower:
				OrderName: clustermissile
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Defense
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 5c0
			ioncannonpower:
				OrderName: ioncannon
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, AirHighValue
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			surgicalstrikepower:
				OrderName: surgicalstrike
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			interceptorpower:
				OrderName: interceptors
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Air
					Attractiveness: 5
					TargetMetric: Value
					CheckRadius: 7c0
			stormpower:
				OrderName: storm
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, AirHighValue
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			clusterminepower:
				OrderName: clustermine
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			timewarppower:
				OrderName: TimeWarp
				MinimumAttractiveness: 1000
				FineScanRadius: 2
				Consideration@1:
					Against: Enemy
					Types: Air, Vehicle, Infantry, Water
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@2:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Ally
					Types: Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 3c0
			empmissilepower:
				OrderName: empmissile
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
			heroesoftheunionpower:
				OrderName: heroesofunion
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Ally
					Types: HeroOfUnionTargetable
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 3c512
			tankdroppower:
				OrderName: tankdrop
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			killzonepower:
				OrderName: killzone
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 8c0
			ironcurtainpower:
				OrderName: ironcurtain
				MinimumAttractiveness: 1000
				FineScanRadius: 2
				Consideration@2:
					Against: Enemy
					Types: Infantry
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Ally
					Types: Vehicle, Tank
					Attractiveness: 5
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@4:
					Against: Ally
					Types: Infantry
					Attractiveness: -2
					TargetMetric: Value
					CheckRadius: 2c0
			atomicammopower:
				OrderName: atomicammo
				MinimumAttractiveness: 2
				Consideration@1:
					Against: Ally
					Types: AtomicAmmoTargetable
					Attractiveness: 1
					CheckRadius: 1c512
			atomicammoiraqpower:
				OrderName: atomicammoiraq
				MinimumAttractiveness: 2
				Consideration@1:
					Against: Ally
					Types: AtomicAmmoTargetable
					Attractiveness: 1
					CheckRadius: 2c0
			nshieldpower:
				OrderName: nshieldorder
				MinimumAttractiveness: 1000
				Consideration@1:
					Against: Ally
					Types: Vehicle
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 4c0
			stealthgenpower:
				OrderName: stealthgen
				MinimumAttractiveness: 2500
				FineScanRadius: 2
				Consideration@1:
					Against: Enemy
					Types: Structure, Vehicle
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@2:
					Against: Enemy
					Types: Infantry
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 2c0
				Consideration@3:
					Against: Ally
					Types: Vehicle
					Attractiveness: 3
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@4:
					Against: Ally
					Types: Infantry
					Attractiveness: -2
					TargetMetric: Value
					CheckRadius: 2c0
			frenzypower:
				OrderName: frenzy
				MinimumAttractiveness: 2500
				Consideration@3:
					Against: Ally
					Types: Vehicle, Infantry
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 3c0
			naniterepairpower:
				OrderName: nrepair
				MinimumAttractiveness: 2000
				Consideration@1:
					Against: Ally
					Types: Vehicle
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 4c0
			veilofwarpower:
				OrderName: veilofwar
				MinimumAttractiveness: 3000
				Consideration@2:
					Against: Enemy
					Types: Infantry, Vehicle
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 8c0
				Consideration@3:
					Against: Ally
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 8c0
			cryostormpower:
				OrderName: cryostorm
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 4c512
			heliosbombpower:
				OrderName: heliosbomb
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Infantry, Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 7c0
			blackskystrikepower:
				OrderName: blackskystrike
				MinimumAttractiveness: 4000
				FineScanRadius: 2
				Consideration@1:
					Against: Enemy
					Types: Vehicle
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 6c0
			stormspikepower:
				OrderName: stormspike
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
			buzzerswarmpower:
				OrderName: buzzerswarmai
				MinimumAttractiveness: 1000
				FineScanRadius: 2
				Consideration@3:
					Against: Enemy
					Types: Infantry
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 2c0
			ionsurgepower:
				OrderName: ionsurge
				MinimumAttractiveness: 2500
				Consideration@1:
					Against: Ally
					Types: Vehicle, Infantry, Air
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 6c0
			owrath:
				OrderName: overlordswrath
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Defense
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 3c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 5c0
			gateway:
				OrderName: gateway
				MinimumAttractiveness: 5
				Consideration@1:
					Against: Enemy
					Types: Structure
					Attractiveness: 1
					TargetMetric: None
					CheckRadius: 8c0
				Consideration@2:
					Against: Enemy
					Types: Water
					Attractiveness: -5
					TargetMetric: None
					CheckRadius: 8c0
			suppressionpower:
				OrderName: suppression
				MinimumAttractiveness: 3000
				FineScanRadius: 2
				Consideration@3:
					Against: Enemy
					Types: Vehicle, Infantry
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 3c0
			suppressionsiphonpower:
				OrderName: suppressionsiphon
				MinimumAttractiveness: 3000
				FineScanRadius: 1
				Consideration@3:
					Against: Enemy
					Types: Vehicle, Infantry
					Attractiveness: 2
					TargetMetric: Value
					CheckRadius: 3c0
			riftpower:
				OrderName: rift
				MinimumAttractiveness: 3000
				Consideration@1:
					Against: Enemy
					Types: Structure, AirHighValue
					Attractiveness: 1
					TargetMetric: Value
					CheckRadius: 5c0
				Consideration@2:
					Against: Ally
					Types: Air, Ground, Water
					Attractiveness: -10
					TargetMetric: Value
					CheckRadius: 7c0
	HarvesterBotModuleCA:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-naval-ai || enable-serious-ai
		HarvesterTypes: harv,harv.td,harv.td.upg,harv.scrin,harv.chrono
		RefineryTypes: proc,proc.td,proc.scrin
	CaptureManagerBotModule:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-serious-ai
		CapturingActorTypes: e6,n6,s6
		CapturableActorTypes: fact,afac,sfac,barr,tent,hand,pyle,port,weap,airs,weap.td,wsph,atek,stek,gtek,tmpl,scrt,dome,hq,hq.upg,nerv,mslo,mslo.nod,weat,eye,rfgn,iron,pdox,sgen,mani,oilb,bio,miss,hosp,fcom
		CheckCaptureTargetsForVisibility: false
		MaximumCaptureTargetOptions: 15
	BridgeRepairBotModule:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-serious-ai
		RepairActorTypes: e6, n6, s6
		EnemyAvoidanceRadius: 4c0
	BuildingRepairBotModuleCA:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-naval-ai || enable-serious-ai
	PowerDownBotModuleCA:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai || enable-hard-ai || enable-normal-ai || enable-easy-ai || enable-naval-ai || enable-serious-ai
	McvManagerBotModuleCA@brutal:
		RequiresCondition: enable-brutal-ai
		McvTypes: mcv, amcv, smcv
		ConstructionYardTypes: fact, afac, sfac
		McvFactoryTypes: weap, weap.td, airs, wsph
		ScanForNewMcvInterval: 80
		MaxBaseRadius: 25
		MinimumConstructionYardCount: 3
	McvManagerBotModuleCA@upper:
		RequiresCondition: enable-vhard-ai || enable-hard-ai
		McvTypes: mcv, amcv, smcv
		ConstructionYardTypes: fact, afac, sfac
		McvFactoryTypes: weap, weap.td, airs, wsph
		ScanForNewMcvInterval: 80
		MaxBaseRadius: 25
		MinimumConstructionYardCount: 2
	McvManagerBotModuleCA@lower:
		RequiresCondition: enable-normal-ai || enable-easy-ai || enable-naval-ai || enable-serious-ai
		McvTypes: mcv, amcv, smcv
		ConstructionYardTypes: fact, afac, sfac
		McvFactoryTypes: weap, weap.td, airs, wsph
	BaseBuilderBotModuleCA@brutal-vhard:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 350
		ExcessPowerIncrement: 10
		ExcessPowerIncreaseThreshold: 1
		PlaceDefenseTowardsEnemyChance: 75
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		MaxBaseRadius: 26
		BuildingIntervals:
			proc: 2250
			proc.td: 2250
			proc.scrin: 2250
			weap: 2250
			weap.td: 2250
			airs: 2250
			wsph: 2250
		BuildingDelays:
			kenn: 2800
			dome: 4500
			hq: 4500
			hq.upg: 4500
			nerv: 4500
			syrd: 6000
			spen: 6000
			syrd.gdi: 6000
			spen.nod: 6000
			afld: 6375
			afld.gdi: 6375
			grav: 6375
			hpad: 6375
			hpad.td: 6375
			atek: 6375
			alhq: 6375
			gtek: 6375
			stek: 6375
			tmpl: 6375
			scrt: 6375
			tmpp: 8000
			upgc: 8000
			cvat: 8000
			indp: 8000
			munp: 8000
			orep: 8000
			sign: 8000
			mslo: 8000
			mslo.nod: 8000
			weat: 8000
			eye: 8000
			rfgn: 8000
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			kenn: 1
			dome: 1
			hq: 1
			hq.upg: 1
			nerv: 1
			weap: 4
			weap.td: 4
			airs: 4
			wsph: 4
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 5
			atek: 1
			alhq: 1
			stek: 1
			gtek: 1
			tmpl: 1
			scrt: 1
			fix: 1
			rep: 1
			srep: 1
			npwr: 1
			spen: 2
			syrd: 2
			spen.nod: 2
			syrd.gdi: 2
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 15
			pyle: 15
			barr: 15
			tent: 15
			port: 15
			kenn: 1
			weap: 10
			weap.td: 10
			airs: 10
			wsph: 10
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 7
			scol: 9
			tsla: 9
			obli: 8
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 7
			atwr: 10
			stwr: 8
			pris: 8
			agun: 9
			cram: 9
			sam: 9
			nsam: 9
			shar: 9
			gap: 1
			atek: 1
			alhq: 1
			stek: 1
			silo: 1
			silo.td: 1
			silo.scrin: 1
			fix: 1
			dome: 10
			tmpl: 1
			gtek: 1
			scrt: 1
			rep: 1
			srep: 1
			hq: 10
			hq.upg: 10
			nerv: 10
			afld: 10
			afld.gdi: 10
			grav: 4
			mslo: 1
			iron: 1
			pdox: 1
			sgen: 1
			sgen.shadow: 1
			mani: 1
			eye: 1
			weat: 1
			mslo.nod: 1
			rfgn: 1
			npwr: 1
			cvat: 1
			indp: 1
			munp: 1
			tmpp: 1
			upgc: 1
			upgc.bomb: 1
			upgc.seek: 1
			upgc.hold: 1
			upgc.drop: 1
			orep: 1
			sign: 1
			patr: 1
			hpad: 10
			hpad.td: 10
			spen: 1
			syrd: 1
			spen.nod: 1
			syrd.gdi: 1
			bombard.strat: 1
			bombard2.strat: 1
			seek.strat: 1
			seek2.strat: 1
			hold.strat: 1
			hold2.strat: 1
			sonic.upgrade: 1
			mdrone.upgrade: 1
			bdrone.upgrade: 1
			abur.upgrade: 1
			bjet.upgrade: 1
			thwk.upgrade: 1
			tow.upgrade: 1
			pointdef.upgrade: 1
			hypersonic.upgrade: 1
			hailstorm.upgrade: 1
			hammerhead.upgrade: 1
			railgun.upgrade: 1
			hovermam.upgrade: 1
			ionmam.upgrade: 1
			vulcan.upgrade: 1
			rapc.upgrade: 1
			empgren.upgrade: 1
			avenger.upgrade: 1
			sidewinders.upgrade: 1
			ceramic.upgrade: 1
			hazmat.upgrade: 1
			hazmatsoviet.upgrade: 1
			howi.upgrade: 1
			microwave.upgrade: 1
			blacknapalm.upgrade: 1
			quantum.upgrade: 1
			tibcore.upgrade: 1
			lastnk.upgrade: 1
			rahstealth.upgrade: 1
			sharv.upgrade: 1
			hstk.upgrade: 1
			advcyber.upgrade: 1
			cyborgdmg.upgrade: 1
			cyborgprod.upgrade: 1
			cyborgspeed.upgrade: 1
			cyborgarmor.upgrade: 1
			seismic.upgrade: 1
			tarc.upgrade: 1
			ttrp.upgrade: 1
			deso.upgrade: 1
			atomicengines.upgrade: 1
			erad.upgrade: 1
			lasher.upgrade: 1
			gattling.upgrade: 1
			v3.upgrade: 1
			charv.upgrade: 1
			pcan.upgrade: 1
			airborne.upgrade: 1
			cryw.upgrade: 1
			apb.upgrade: 1
			sweden.coalition: 1
			korea.coalition: 1
			greece.coalition: 1
			economy.policy: 1
			defense.policy: 1
			development.policy: 1
			flakarmor.upgrade: 1
			carapace.upgrade: 1
			advart.upgrade: 1
			shrw.upgrade: 1
			loyalist.allegiance: 1
			rebel.allegiance: 1
			malefic.allegiance: 1
			evis.upgrade: 1
			impl.upgrade: 1
			stlk.upgrade: 1
			stellar.upgrade: 1
			coalescence.upgrade: 1
			resconv.upgrade: 1
			ioncon.upgrade: 1
			regen.upgrade: 1
			shields.upgrade: 1
			infantry.doctrine: 1
			armor.doctrine: 1
			arty.doctrine: 1
			imppara.upgrade: 1
			impstorm.upgrade: 1
			impmuta.upgrade: 1
			reactive.upgrade: 1
			rocketpods.upgrade: 1
			ovld.upgrade: 1
			apoc.upgrade: 1
			nukc.upgrade: 1
			wrath.covenant: 1
			unity.covenant: 1
			zeal.covenant: 1
	BaseBuilderBotModuleCA@hard:
		RequiresCondition: enable-hard-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 290
		ExcessPowerIncrement: 30
		ExcessPowerIncreaseThreshold: 3
		PlaceDefenseTowardsEnemyChance: 75
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		MaxBaseRadius: 23
		BuildingIntervals:
			proc: 2250
			proc.td: 2250
			proc.scrin: 2250
			weap: 2250
			weap.td: 2250
			airs: 2250
			wsph: 2250
		BuildingDelays:
			kenn: 2800
			dome: 4500
			hq: 4500
			hq.upg: 4500
			nerv: 4500
			syrd: 6000
			spen: 6000
			syrd.gdi: 6000
			spen.nod: 6000
			afld: 6375
			afld.gdi: 6375
			grav: 6375
			hpad: 6375
			hpad.td: 6375
			atek: 6375
			alhq: 6375
			gtek: 6375
			stek: 6375
			tmpl: 6375
			scrt: 6375
			tmpp: 8000
			upgc: 8000
			cvat: 8000
			indp: 8000
			munp: 8000
			orep: 8000
			sign: 8000
			mslo: 8000
			mslo.nod: 8000
			weat: 8000
			eye: 8000
			rfgn: 8000
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			kenn: 1
			dome: 1
			hq: 1
			hq.upg: 1
			nerv: 1
			weap: 4
			weap.td: 4
			airs: 4
			wsph: 4
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 5
			atek: 1
			alhq: 1
			stek: 1
			gtek: 1
			tmpl: 1
			scrt: 1
			fix: 1
			rep: 1
			srep: 1
			npwr: 1
			spen: 2
			syrd: 2
			spen.nod: 2
			syrd.gdi: 2
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 15
			pyle: 15
			barr: 15
			tent: 15
			port: 15
			kenn: 1
			weap: 10
			weap.td: 10
			airs: 10
			wsph: 10
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 7
			scol: 7
			tsla: 7
			obli: 6
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 5
			atwr: 8
			stwr: 6
			pris: 6
			agun: 7
			cram: 7
			sam: 7
			nsam: 7
			shar: 7
			gap: 1
			atek: 1
			alhq: 1
			stek: 1
			silo: 1
			silo.td: 1
			silo.scrin: 1
			fix: 1
			dome: 10
			tmpl: 1
			gtek: 1
			scrt: 1
			rep: 1
			srep: 1
			hq: 10
			hq.upg: 10
			nerv: 10
			afld: 10
			afld.gdi: 10
			grav: 4
			mslo: 1
			iron: 1
			pdox: 1
			sgen: 1
			sgen.shadow: 1
			mani: 1
			eye: 1
			weat: 1
			mslo.nod: 1
			rfgn: 1
			npwr: 1
			cvat: 1
			indp: 1
			munp: 1
			tmpp: 1
			upgc: 1
			upgc.bomb: 1
			upgc.seek: 1
			upgc.hold: 1
			upgc.drop: 1
			orep: 1
			sign: 1
			patr: 1
			hpad: 10
			hpad.td: 10
			spen: 1
			syrd: 1
			spen.nod: 1
			syrd.gdi: 1
			bombard.strat: 1
			bombard2.strat: 1
			seek.strat: 1
			seek2.strat: 1
			hold.strat: 1
			hold2.strat: 1
			sonic.upgrade: 1
			mdrone.upgrade: 1
			bdrone.upgrade: 1
			abur.upgrade: 1
			bjet.upgrade: 1
			thwk.upgrade: 1
			tow.upgrade: 1
			pointdef.upgrade: 1
			hypersonic.upgrade: 1
			hailstorm.upgrade: 1
			hammerhead.upgrade: 1
			railgun.upgrade: 1
			hovermam.upgrade: 1
			ionmam.upgrade: 1
			vulcan.upgrade: 1
			rapc.upgrade: 1
			empgren.upgrade: 1
			avenger.upgrade: 1
			sidewinders.upgrade: 1
			ceramic.upgrade: 1
			hazmat.upgrade: 1
			hazmatsoviet.upgrade: 1
			howi.upgrade: 1
			microwave.upgrade: 1
			blacknapalm.upgrade: 1
			quantum.upgrade: 1
			tibcore.upgrade: 1
			lastnk.upgrade: 1
			rahstealth.upgrade: 1
			sharv.upgrade: 1
			hstk.upgrade: 1
			advcyber.upgrade: 1
			cyborgdmg.upgrade: 1
			cyborgprod.upgrade: 1
			cyborgspeed.upgrade: 1
			cyborgarmor.upgrade: 1
			seismic.upgrade: 1
			tarc.upgrade: 1
			ttrp.upgrade: 1
			deso.upgrade: 1
			atomicengines.upgrade: 1
			erad.upgrade: 1
			lasher.upgrade: 1
			gattling.upgrade: 1
			v3.upgrade: 1
			charv.upgrade: 1
			pcan.upgrade: 1
			airborne.upgrade: 1
			cryw.upgrade: 1
			apb.upgrade: 1
			sweden.coalition: 1
			korea.coalition: 1
			greece.coalition: 1
			economy.policy: 1
			defense.policy: 1
			development.policy: 1
			flakarmor.upgrade: 1
			carapace.upgrade: 1
			advart.upgrade: 1
			shrw.upgrade: 1
			loyalist.allegiance: 1
			rebel.allegiance: 1
			malefic.allegiance: 1
			evis.upgrade: 1
			impl.upgrade: 1
			stlk.upgrade: 1
			stellar.upgrade: 1
			coalescence.upgrade: 1
			resconv.upgrade: 1
			ioncon.upgrade: 1
			regen.upgrade: 1
			shields.upgrade: 1
			infantry.doctrine: 1
			armor.doctrine: 1
			arty.doctrine: 1
			imppara.upgrade: 1
			impstorm.upgrade: 1
			impmuta.upgrade: 1
			reactive.upgrade: 1
			rocketpods.upgrade: 1
			ovld.upgrade: 1
			apoc.upgrade: 1
			nukc.upgrade: 1
			wrath.covenant: 1
			unity.covenant: 1
			zeal.covenant: 1
	BaseBuilderBotModuleCA@normal:
		RequiresCondition: enable-normal-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 200
		ExcessPowerIncrement: 40
		ExcessPowerIncreaseThreshold: 4
		PlaceDefenseTowardsEnemyChance: 75
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		BuildingIntervals:
			proc: 2250
			proc.td: 2250
			proc.scrin: 2250
			weap: 2250
			weap.td: 2250
			airs: 2250
			wsph: 2250
		BuildingDelays:
			kenn: 2800
			dome: 4500
			hq: 4500
			hq.upg: 4500
			nerv: 4500
			syrd: 6000
			spen: 6000
			syrd.gdi: 6000
			spen.nod: 6000
			afld: 6375
			afld.gdi: 6375
			grav: 6375
			hpad: 6375
			hpad.td: 6375
			atek: 6375
			alhq: 6375
			gtek: 6375
			stek: 6375
			tmpl: 6375
			scrt: 6375
			tmpp: 8000
			upgc: 8000
			cvat: 8000
			indp: 8000
			munp: 8000
			orep: 8000
			sign: 8000
			mslo: 8000
			mslo.nod: 8000
			weat: 8000
			eye: 8000
			rfgn: 8000
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			kenn: 1
			dome: 1
			hq: 1
			hq.upg: 1
			nerv: 1
			weap: 4
			weap.td: 4
			airs: 4
			wsph: 4
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 5
			atek: 1
			alhq: 1
			stek: 1
			gtek: 1
			tmpl: 1
			scrt: 1
			fix: 1
			rep: 1
			srep: 1
			npwr: 1
			spen: 2
			syrd: 2
			spen.nod: 2
			syrd.gdi: 2
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 15
			pyle: 15
			barr: 15
			tent: 15
			port: 15
			kenn: 1
			weap: 10
			weap.td: 10
			airs: 10
			wsph: 10
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 5
			scol: 5
			tsla: 5
			obli: 5
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 3
			atwr: 7
			stwr: 5
			pris: 5
			agun: 5
			cram: 5
			sam: 5
			nsam: 5
			shar: 5
			gap: 1
			atek: 1
			alhq: 1
			stek: 1
			silo: 1
			silo.td: 1
			silo.scrin: 1
			fix: 1
			dome: 10
			tmpl: 1
			gtek: 1
			scrt: 1
			rep: 1
			srep: 1
			hq: 10
			hq.upg: 10
			nerv: 10
			afld: 10
			afld.gdi: 10
			grav: 4
			mslo: 1
			iron: 1
			pdox: 1
			sgen: 1
			sgen.shadow: 1
			mani: 1
			eye: 1
			weat: 1
			mslo.nod: 1
			rfgn: 1
			npwr: 1
			cvat: 1
			indp: 1
			munp: 1
			tmpp: 1
			upgc: 1
			upgc.bomb: 1
			upgc.seek: 1
			upgc.hold: 1
			upgc.drop: 1
			orep: 1
			sign: 1
			patr: 1
			hpad: 10
			hpad.td: 10
			spen: 1
			syrd: 1
			spen.nod: 1
			syrd.gdi: 1
			bombard.strat: 1
			bombard2.strat: 1
			seek.strat: 1
			seek2.strat: 1
			hold.strat: 1
			hold2.strat: 1
			sonic.upgrade: 1
			mdrone.upgrade: 1
			bdrone.upgrade: 1
			abur.upgrade: 1
			bjet.upgrade: 1
			thwk.upgrade: 1
			tow.upgrade: 1
			pointdef.upgrade: 1
			hypersonic.upgrade: 1
			hailstorm.upgrade: 1
			hammerhead.upgrade: 1
			railgun.upgrade: 1
			hovermam.upgrade: 1
			ionmam.upgrade: 1
			vulcan.upgrade: 1
			rapc.upgrade: 1
			empgren.upgrade: 1
			avenger.upgrade: 1
			sidewinders.upgrade: 1
			ceramic.upgrade: 1
			hazmat.upgrade: 1
			hazmatsoviet.upgrade: 1
			howi.upgrade: 1
			microwave.upgrade: 1
			blacknapalm.upgrade: 1
			quantum.upgrade: 1
			tibcore.upgrade: 1
			lastnk.upgrade: 1
			rahstealth.upgrade: 1
			sharv.upgrade: 1
			hstk.upgrade: 1
			advcyber.upgrade: 1
			cyborgdmg.upgrade: 1
			cyborgprod.upgrade: 1
			cyborgspeed.upgrade: 1
			cyborgarmor.upgrade: 1
			seismic.upgrade: 1
			tarc.upgrade: 1
			ttrp.upgrade: 1
			deso.upgrade: 1
			atomicengines.upgrade: 1
			erad.upgrade: 1
			lasher.upgrade: 1
			gattling.upgrade: 1
			v3.upgrade: 1
			charv.upgrade: 1
			pcan.upgrade: 1
			airborne.upgrade: 1
			cryw.upgrade: 1
			apb.upgrade: 1
			sweden.coalition: 1
			korea.coalition: 1
			greece.coalition: 1
			economy.policy: 1
			defense.policy: 1
			development.policy: 1
			flakarmor.upgrade: 1
			carapace.upgrade: 1
			advart.upgrade: 1
			shrw.upgrade: 1
			loyalist.allegiance: 1
			rebel.allegiance: 1
			malefic.allegiance: 1
			evis.upgrade: 1
			impl.upgrade: 1
			stlk.upgrade: 1
			stellar.upgrade: 1
			coalescence.upgrade: 1
			resconv.upgrade: 1
			ioncon.upgrade: 1
			regen.upgrade: 1
			shields.upgrade: 1
			infantry.doctrine: 1
			armor.doctrine: 1
			arty.doctrine: 1
			imppara.upgrade: 1
			impstorm.upgrade: 1
			impmuta.upgrade: 1
			reactive.upgrade: 1
			rocketpods.upgrade: 1
			ovld.upgrade: 1
			apoc.upgrade: 1
			nukc.upgrade: 1
			wrath.covenant: 1
			unity.covenant: 1
			zeal.covenant: 1
	BaseBuilderBotModuleCA@easy:
		RequiresCondition: enable-easy-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 200
		ExcessPowerIncrement: 50
		ExcessPowerIncreaseThreshold: 5
		PlaceDefenseTowardsEnemyChance: 75
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		BuildingIntervals:
			proc: 2250
			proc.td: 2250
			proc.scrin: 2250
			weap: 2250
			weap.td: 2250
			airs: 2250
			wsph: 2250
		BuildingDelays:
			kenn: 2800
			dome: 4500
			hq: 4500
			hq.upg: 4500
			nerv: 4500
			syrd: 6000
			spen: 6000
			syrd.gdi: 6000
			spen.nod: 6000
			afld: 6375
			afld.gdi: 6375
			grav: 6375
			hpad: 6375
			hpad.td: 6375
			atek: 6375
			alhq: 6375
			gtek: 6375
			stek: 6375
			tmpl: 6375
			scrt: 6375
			tmpp: 8000
			upgc: 8000
			cvat: 8000
			indp: 8000
			munp: 8000
			orep: 8000
			sign: 8000
			mslo: 8000
			mslo.nod: 8000
			weat: 8000
			eye: 8000
			rfgn: 8000
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			kenn: 1
			dome: 1
			hq: 1
			hq.upg: 10
			nerv: 1
			weap: 4
			weap.td: 4
			airs: 4
			wsph: 4
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 5
			atek: 1
			alhq: 1
			stek: 1
			gtek: 1
			tmpl: 1
			scrt: 1
			fix: 1
			rep: 1
			srep: 1
			npwr: 1
			spen: 2
			syrd: 2
			spen.nod: 2
			syrd.gdi: 2
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 3
			pyle: 3
			barr: 3
			tent: 3
			port: 3
			kenn: 1
			weap: 3
			weap.td: 3
			airs: 3
			wsph: 3
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 7
			scol: 5
			tsla: 5
			obli: 5
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 3
			atwr: 7
			stwr: 5
			pris: 5
			agun: 3
			cram: 3
			sam: 3
			nsam: 3
			shar: 3
			gap: 1
			atek: 1
			alhq: 1
			stek: 1
			silo: 1
			silo.td: 1
			silo.scrin: 1
			fix: 1
			dome: 10
			tmpl: 1
			gtek: 1
			scrt: 1
			rep: 1
			srep: 1
			hq: 10
			hq.upg: 10
			nerv: 1
			afld: 10
			afld.gdi: 10
			grav: 3
			mslo: 1
			iron: 1
			pdox: 1
			sgen: 1
			sgen.shadow: 1
			mani: 1
			eye: 1
			weat: 1
			mslo.nod: 1
			rfgn: 1
			npwr: 1
			cvat: 1
			indp: 1
			munp: 1
			tmpp: 1
			upgc: 1
			upgc.bomb: 1
			upgc.seek: 1
			upgc.hold: 1
			upgc.drop: 1
			orep: 1
			sign: 1
			patr: 1
			hpad: 10
			hpad.td: 10
			spen: 1
			syrd: 1
			spen.nod: 1
			syrd.gdi: 1
			bombard.strat: 1
			bombard2.strat: 1
			seek.strat: 1
			seek2.strat: 1
			hold.strat: 1
			hold2.strat: 1
			sonic.upgrade: 1
			mdrone.upgrade: 1
			bdrone.upgrade: 1
			abur.upgrade: 1
			bjet.upgrade: 1
			thwk.upgrade: 1
			tow.upgrade: 1
			pointdef.upgrade: 1
			hypersonic.upgrade: 1
			hailstorm.upgrade: 1
			hammerhead.upgrade: 1
			railgun.upgrade: 1
			hovermam.upgrade: 1
			ionmam.upgrade: 1
			vulcan.upgrade: 1
			rapc.upgrade: 1
			empgren.upgrade: 1
			avenger.upgrade: 1
			sidewinders.upgrade: 1
			ceramic.upgrade: 1
			hazmat.upgrade: 1
			hazmatsoviet.upgrade: 1
			howi.upgrade: 1
			microwave.upgrade: 1
			blacknapalm.upgrade: 1
			quantum.upgrade: 1
			tibcore.upgrade: 1
			lastnk.upgrade: 1
			rahstealth.upgrade: 1
			sharv.upgrade: 1
			hstk.upgrade: 1
			advcyber.upgrade: 1
			cyborgdmg.upgrade: 1
			cyborgprod.upgrade: 1
			cyborgspeed.upgrade: 1
			cyborgarmor.upgrade: 1
			seismic.upgrade: 1
			tarc.upgrade: 1
			ttrp.upgrade: 1
			deso.upgrade: 1
			atomicengines.upgrade: 1
			erad.upgrade: 1
			lasher.upgrade: 1
			gattling.upgrade: 1
			v3.upgrade: 1
			charv.upgrade: 1
			pcan.upgrade: 1
			airborne.upgrade: 1
			cryw.upgrade: 1
			apb.upgrade: 1
			sweden.coalition: 1
			korea.coalition: 1
			greece.coalition: 1
			economy.policy: 1
			defense.policy: 1
			development.policy: 1
			flakarmor.upgrade: 1
			carapace.upgrade: 1
			advart.upgrade: 1
			shrw.upgrade: 1
			loyalist.allegiance: 1
			rebel.allegiance: 1
			malefic.allegiance: 1
			evis.upgrade: 1
			impl.upgrade: 1
			stlk.upgrade: 1
			stellar.upgrade: 1
			coalescence.upgrade: 1
			resconv.upgrade: 1
			ioncon.upgrade: 1
			regen.upgrade: 1
			shields.upgrade: 1
			infantry.doctrine: 1
			armor.doctrine: 1
			arty.doctrine: 1
			imppara.upgrade: 1
			impstorm.upgrade: 1
			impmuta.upgrade: 1
			reactive.upgrade: 1
			rocketpods.upgrade: 1
			ovld.upgrade: 1
			apoc.upgrade: 1
			nukc.upgrade: 1
			wrath.covenant: 1
			unity.covenant: 1
			zeal.covenant: 1
	BaseBuilderBotModuleCA@naval:
		RequiresCondition: enable-naval-ai
		BuildingQueues: BuildingSQ, BuildingMQ, Upgrade
		DefenseQueues: DefenseSQ, DefenseMQ
		MinimumExcessPower: 50
		MaximumExcessPower: 300
		ExcessPowerIncrement: 20
		ExcessPowerIncreaseThreshold: 2
		PlaceDefenseTowardsEnemyChance: 75
		ConstructionYardTypes: fact,afac,sfac
		RefineryTypes: proc,proc.td,proc.scrin
		PowerTypes: powr,apwr,nuke,nuk2,tpwr,reac,rea2
		BarracksTypes: barr,tent,hand,pyle,port
		VehiclesFactoryTypes: weap,airs,weap.td,wsph
		ProductionTypes: barr,tent,hand,pyle,port,airs,weap,weap.td,wsph,afld,afld.gdi,hpad,hpad.td,grav
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		SiloTypes: silo,silo.td,silo.scrin
		AntiAirTypes: sam, nsam, agun, cram, shar
		DefenseTypes: pbox, hbox, htur, gun, gun.td, gtwr, atwr, ptur, scol, obli, ltur, stwr, tsla, ftur, ttur, pris, sam, nsam, agun, cram, shar
		BuildingIntervals:
			proc: 2250
			proc.td: 2250
			proc.scrin: 2250
			weap: 2250
			weap.td: 2250
			airs: 2250
			wsph: 2250
		BuildingLimits:
			barr: 5
			tent: 5
			pyle: 5
			hand: 5
			port: 5
			kenn: 1
			dome: 1
			hq: 1
			hq.upg: 1
			nerv: 1
			weap: 3
			weap.td: 3
			airs: 3
			wsph: 3
			hpad: 5
			hpad.td: 5
			afld: 5
			afld.gdi: 5
			grav: 3
			atek: 1
			alhq: 1
			stek: 1
			gtek: 1
			tmpl: 1
			scrt: 1
			fix: 1
			rep: 1
			srep: 1
			npwr: 1
			spen: 2
			syrd: 2
			spen.nod: 2
			syrd.gdi: 2
		BuildingFractions:
			proc: 30
			proc.td: 30
			proc.scrin: 30
			hand: 5
			pyle: 5
			barr: 5
			tent: 5
			port: 5
			kenn: 1
			weap: 5
			weap.td: 5
			airs: 5
			wsph: 5
			pbox: 7
			hbox: 7
			gtwr: 7
			gun: 7
			gun.td: 7
			ptur: 7
			scol: 5
			tsla: 5
			obli: 5
			ltur: 10
			ftur: 10
			ttur: 10
			htur: 3
			atwr: 7
			stwr: 5
			pris: 5
			agun: 3
			cram: 3
			sam: 3
			nsam: 3
			shar: 3
			gap: 1
			atek: 1
			alhq: 1
			stek: 1
			fix: 1
			dome: 10
			tmpl: 1
			gtek: 1
			scrt: 1
			rep: 1
			srep: 1
			hq: 10
			hq.upg: 10
			nerv: 10
			afld: 10
			afld.gdi: 10
			grav: 3
			mslo: 1
			iron: 1
			pdox: 1
			sgen: 1
			sgen.shadow: 1
			mani: 1
			npwr: 1
			cvat: 1
			indp: 1
			munp: 1
			tmpp: 1
			upgc: 1
			upgc.bomb: 1
			upgc.seek: 1
			upgc.hold: 1
			upgc.drop: 1
			orep: 1
			sign: 1
			patr: 1
			weat: 1
			mslo.nod: 1
			rfgn: 1
			eye: 1
			hpad: 10
			hpad.td: 10
			spen: 1
			syrd: 1
			spen.nod: 1
			syrd.gdi: 1
			bombard.strat: 1
			bombard2.strat: 1
			seek.strat: 1
			seek2.strat: 1
			hold.strat: 1
			hold2.strat: 1
			sonic.upgrade: 1
			mdrone.upgrade: 1
			bdrone.upgrade: 1
			abur.upgrade: 1
			bjet.upgrade: 1
			thwk.upgrade: 1
			tow.upgrade: 1
			pointdef.upgrade: 1
			hypersonic.upgrade: 1
			hailstorm.upgrade: 1
			hammerhead.upgrade: 1
			railgun.upgrade: 1
			hovermam.upgrade: 1
			ionmam.upgrade: 1
			vulcan.upgrade: 1
			rapc.upgrade: 1
			empgren.upgrade: 1
			avenger.upgrade: 1
			sidewinders.upgrade: 1
			ceramic.upgrade: 1
			hazmat.upgrade: 1
			hazmatsoviet.upgrade: 1
			howi.upgrade: 1
			microwave.upgrade: 1
			blacknapalm.upgrade: 1
			quantum.upgrade: 1
			tibcore.upgrade: 1
			lastnk.upgrade: 1
			rahstealth.upgrade: 1
			sharv.upgrade: 1
			hstk.upgrade: 1
			advcyber.upgrade: 1
			cyborgdmg.upgrade: 1
			cyborgprod.upgrade: 1
			cyborgspeed.upgrade: 1
			cyborgarmor.upgrade: 1
			seismic.upgrade: 1
			tarc.upgrade: 1
			ttrp.upgrade: 1
			deso.upgrade: 1
			atomicengines.upgrade: 1
			erad.upgrade: 1
			lasher.upgrade: 1
			gattling.upgrade: 1
			v3.upgrade: 1
			charv.upgrade: 1
			pcan.upgrade: 1
			airborne.upgrade: 1
			cryw.upgrade: 1
			apb.upgrade: 1
			sweden.coalition: 1
			korea.coalition: 1
			greece.coalition: 1
			economy.policy: 1
			defense.policy: 1
			development.policy: 1
			flakarmor.upgrade: 1
			carapace.upgrade: 1
			advart.upgrade: 1
			shrw.upgrade: 1
			loyalist.allegiance: 1
			rebel.allegiance: 1
			malefic.allegiance: 1
			evis.upgrade: 1
			impl.upgrade: 1
			stlk.upgrade: 1
			stellar.upgrade: 1
			coalescence.upgrade: 1
			resconv.upgrade: 1
			ioncon.upgrade: 1
			regen.upgrade: 1
			shields.upgrade: 1
			infantry.doctrine: 1
			armor.doctrine: 1
			arty.doctrine: 1
			imppara.upgrade: 1
			impstorm.upgrade: 1
			impmuta.upgrade: 1
			reactive.upgrade: 1
			rocketpods.upgrade: 1
			ovld.upgrade: 1
			apoc.upgrade: 1
			nukc.upgrade: 1
			wrath.covenant: 1
			unity.covenant: 1
			zeal.covenant: 1
	SquadManagerBotModuleCA@brutal-vhard:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai
		MinimumAttackForceDelay: 25
		SquadValue: 7000
		SquadValueRandomBonus: 3000
		SquadSize: 25
		SquadSizeRandomBonus: 11
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,dd2,pt2,ss2,isub,sb,seas
		NavalProductionTypes: syrd, spen, syrd.gdi, spen.nod
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		IdleScanRadius: 18
		AttackScanRadius: 9
		MaxBaseRadius: 16
		ProtectUnitScanRadius: 28
		ProtectionScanRadius: 16
		HighValueTargetTypes: fact, afac, sfac, proc, proc.td, proc.scrin, atek, stek, gtek, tmpl, scrt
		HighValueTargetPriority: 20
		AirToAirPriority: 60
		BigAirThreats: pac, deva, kiro, mshp
		AirSquadTargetArmorTypes:
			heli: Aircraft, Heavy
			nhaw: None
			hind: None, Heavy, Light
			yak: Wood, None, Aircraft, Light
			mig: Aircraft, Heavy, Light, Concrete
			pmak: Heavy, Concrete
			beag: Aircraft, Heavy, Light, Concrete
			suk: Heavy, Concrete
			suk.upg: Heavy, Wood
			kiro: Wood
			orca: Aircraft, Heavy
			orcb: Heavy, Light, Concrete
			a10: None, Wood
			a10.sw: None, Wood, Aircraft
			a10.gau: None, Wood, Light
			auro: Heavy, Concrete, Wood
			jack: Heavy, Light, Concrete
			apch: None, Wood, Aircraft, Light
			venm: None, Aircraft, Light
			rah: None, Wood, Light
			harr: None, Wood, Aircraft, Light
			scrn: Aircraft, Heavy, Concrete
			stmr: Aircraft, None, Wood, Light
			torm: Aircraft, Heavy
			enrv: Aircraft, Heavy, Concrete
			mshp: Wood
	UnitBuilderBotModuleCA@brutal-vhard:
		RequiresCondition: enable-brutal-ai || enable-vhard-ai
		IdleBaseUnitsMaximum: 50
		MaxAircraft: 3
		MaintainAirSuperiority: true
		AirToAirUnits: heli, yak, mig, orca, apch, scrn, venm, stmr, torm, enrv, a10.sw, a10.gau, harr, beag
		AirThreatUnits: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, apch, venm, rah, scrn, stmr, enrv, mshp, phan, kamv, shde, vert, mcor
		ProductionMinCashRequirement: 1800
		UnitIntervals:
			mshp: 7500
			mcv: 3000
			amcv: 3000
			smcv: 3000
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
			rmbo: 2250
			e7: 2250
			mast: 2250
			yuri: 2250
			bori: 2250
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e6: 5
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n6: 5
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			avtr: 15
			mort.chem: 15
			medi: 3
			mech: 3
			dog: 2
			shok: 15
			ttrp: 15
			brut: 15
			e8: 15
			deso: 15
			snip: 15
			jjet: 15
			bjet: 15
			acol: 15
			tplr: 15
			bh: 15
			ztrp: 15
			zrai: 15
			zdef: 15
			enfo: 15
			hopl: 15
			tigr: 15
			cryt: 15
			evis: 15
			impl: 15
			stlk: 15
			ivan: 15
			cmsr: 15
			rmbo: 15
			e7: 15
			seal: 15
			bori: 15
			yuri: 15
			mast: 15
			apc.ai: 20
			sapc.ai: 10
			sapc.ai2: 10
			intl.ai: 10
			intl.ai2: 10
			jeep: 30
			apc2.nodai: 20
			apc2.gdiai: 20
			rapc.ai: 20
			vulc.ai: 10
			hmmv: 30
			gdrn: 30
			mdrn: 20
			xo: 20
			wolv: 20
			pbul: 20
			jack: 10
			btr: 20
			btr.ai: 10
			btr.yuri: 20
			btr.yuri.ai: 10
			trpc: 10
			gunw: 20
			shrw: 20
			bggy: 30
			arty: 20
			howi: 20
			ptnk: 20
			pcan: 20
			v2rl: 20
			katy: 20
			grad: 20
			nukc: 20
			msam: 25
			mlrs: 20
			spec: 20
			hsam: 25
			ruin: 20
			atmz: 20
			1tnk: 70
			ifv.ai: 70
			2tnk: 45
			gtnk.squad: 20
			tnkd: 45
			rtnk: 45
			3tnk: 70
			3tnk.atomic: 70
			3tnk.yuri: 70
			3tnk.atomicyuri: 70
			3tnk.rhino: 70
			3tnk.rhino.atomic: 70
			3tnk.rhino.yuri: 70
			3tnk.rhino.atomicyuri: 70
			4tnk: 40
			4tnk.atomic: 40
			4tnk.erad: 40
			4tnk.erad.atomic: 40
			apoc: 30
			apoc.atomic: 30
			apoc.erad: 30
			apoc.erad.atomic: 30
			ovld: 30
			ovld.atomic: 30
			ovld.erad: 30
			ovld.erad.atomic: 30
			tpod: 40
			rtpd: 40
			ltnk: 40
			ltnk.laser: 40
			mtnk: 70
			mtnk.drone: 70
			mtnk.laser: 70
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			bike: 30
			htnk: 40
			htnk.ion: 40
			htnk.hover: 40
			htnk.drone: 40
			v3rl: 3
			thwk: 3
			zeus: 3
			titn: 40
			titn.rail: 40
			jugg: 10
			cryo: 10
			mgg: 3
			mrj: 3
			cdrn: 3
			nhaw: 3
			ctnk: 3
			chpr: 3
			batf.ai: 10
			wtnk: 3
			ttnk: 25
			ttra: 25
			isu: 20
			stnk.nod: 15
			hstk: 15
			ftnk: 15
			hftk: 15
			corr: 15
			lchr: 15
			stcr: 25
			oblt: 3
			null: 3
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 1
			msub: 1
			dd: 1
			ca: 1
			cv: 1
			pt: 1
			pt2: 1
			dd2: 1
			ss2: 2
			isub: 1
			sb: 1
			seas: 1
			stmr: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
		UnitLimits:
			e2: 5
			n2: 8
			e4: 5
			n4: 5
			dog: 2
			cmsr: 1
			mgg: 1
			mrj: 1
			cdrn: 1
			nhaw: 1
			apc.ai: 2
			apc2.nodai: 2
			apc2.gdiai: 2
			rapc.ai: 2
			vulc.ai: 5
			btr.ai: 5
			btr.yuri.ai: 5
			hmmv: 2
			bggy: 2
			jeep: 2
			sapc.ai: 1
			sapc.ai2: 1
			e6: 1
			n6: 1
			s6: 1
			u3.squad: 2
			seal: 5
			mech: 3
			medi: 3
			msub: 4
			ca: 4
			isub: 4
			cv: 4
			jjet: 8
			bjet: 8
			v3rl: 4
			nukc: 4
			thwk: 4
			zeus: 4
			oblt: 4
			kiro: 4
			disc: 4
			deva: 4
			pac: 4
			ss: 10
			ss2: 10
			sb: 10
			seas: 10
			harv: 8
			harv.td: 8
			harv.td.upg: 8
			harv.scrin: 8
			harv.chrono: 8
	SquadManagerBotModuleCA@hard:
		RequiresCondition: enable-hard-ai
		MinimumAttackForceDelay: 25
		SquadValue: 6000
		SquadValueRandomBonus: 2500
		SquadSize: 22
		SquadSizeRandomBonus: 9
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,dd2,pt2,ss2,isub,sb,seas
		NavalProductionTypes: syrd, spen, syrd.gdi, spen.nod
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		ProtectionScanRadius: 12
		HighValueTargetTypes: fact, afac, sfac, proc, proc.td, proc.scrin, atek, stek, gtek, tmpl, scrt
		HighValueTargetPriority: 10
		AirToAirPriority: 50
		BigAirThreats: pac, deva, kiro, mshp
		AirSquadTargetArmorTypes:
			heli: Aircraft, Heavy
			nhaw: None
			hind: None, Heavy, Light
			yak: Wood, None, Aircraft, Light
			mig: Aircraft, Heavy, Light, Concrete
			pmak: Heavy, Concrete
			beag: Aircraft, Heavy, Light, Concrete
			suk: Heavy, Concrete
			suk.upg: Heavy, Wood
			kiro: Wood
			orca: Aircraft, Heavy
			orcb: Heavy, Light, Concrete
			a10: None, Wood
			a10.sw: None, Wood, Aircraft
			a10.gau: None, Wood, Light
			auro: Heavy, Concrete, Wood
			jack: Heavy, Light, Concrete
			apch: None, Wood, Aircraft, Light
			venm: None, Aircraft, Light
			rah: None, Wood, Light
			harr: None, Wood, Aircraft, Light
			scrn: Aircraft, Heavy, Concrete
			stmr: Aircraft, None, Wood, Light
			torm: Aircraft, Heavy
			enrv: Aircraft, Heavy, Concrete
			mshp: Wood
	UnitBuilderBotModuleCA@hard:
		RequiresCondition: enable-hard-ai
		IdleBaseUnitsMaximum: 50
		MaxAircraft: 2
		MaintainAirSuperiority: true
		MaxAirSuperiority: 8
		AirToAirUnits: heli, yak, mig, orca, apch, scrn, venm, stmr, torm, enrv, a10.sw, a10.gau, harr, beag
		AirThreatUnits: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, apch, venm, rah, scrn, stmr, enrv, mshp, phan, kamv, shde, vert, mcor
		UnitDelays:
			arty: 4500
			arty.nod: 4500
			katy: 4500
		UnitIntervals:
			mshp: 7500
			mcv: 3750
			amcv: 3750
			smcv: 3750
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
			rmbo: 3000
			e7: 3000
			mast: 3000
			yuri: 3000
			bori: 3000
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e6: 5
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n6: 5
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			avtr: 15
			mort.chem: 15
			medi: 3
			mech: 3
			dog: 2
			shok: 15
			ttrp: 15
			brut: 15
			e8: 15
			deso: 15
			snip: 15
			jjet: 15
			bjet: 15
			acol: 15
			tplr: 15
			bh: 15
			ztrp: 15
			zrai: 15
			zdef: 15
			enfo: 15
			hopl: 15
			tigr: 15
			cryt: 15
			evis: 15
			impl: 15
			stlk: 15
			ivan: 15
			cmsr: 15
			rmbo: 15
			e7: 15
			seal: 15
			bori: 15
			yuri: 15
			mast: 15
			apc.ai: 20
			sapc.ai: 10
			sapc.ai2: 10
			intl.ai: 10
			intl.ai2: 10
			jeep: 30
			apc2.nodai: 20
			apc2.gdiai: 20
			vulc.ai: 10
			hmmv: 30
			gdrn: 30
			mdrn: 20
			xo: 20
			wolv: 20
			pbul: 20
			jack: 10
			btr: 20
			btr.ai: 10
			btr.yuri: 20
			btr.yuri.ai: 10
			trpc: 10
			gunw: 20
			shrw: 20
			bggy: 30
			arty: 20
			howi: 20
			ptnk: 20
			pcan: 20
			v2rl: 20
			katy: 20
			grad: 20
			nukc: 20
			msam: 25
			mlrs: 20
			spec: 20
			hsam: 25
			ruin: 20
			atmz: 20
			1tnk: 70
			ifv.ai: 70
			2tnk: 45
			gtnk.squad: 20
			tnkd: 45
			rtnk: 45
			3tnk: 70
			3tnk.atomic: 70
			3tnk.yuri: 70
			3tnk.atomicyuri: 70
			3tnk.rhino: 70
			3tnk.rhino.atomic: 70
			3tnk.rhino.yuri: 70
			3tnk.rhino.atomicyuri: 70
			4tnk: 40
			4tnk.atomic: 40
			4tnk.erad: 40
			4tnk.erad.atomic: 40
			apoc: 30
			apoc.atomic: 30
			apoc.erad: 30
			apoc.erad.atomic: 30
			ovld: 30
			ovld.atomic: 30
			ovld.erad: 30
			ovld.erad.atomic: 30
			tpod: 40
			rtpd: 40
			ltnk: 40
			ltnk.laser: 40
			mtnk: 70
			mtnk.drone: 70
			mtnk.laser: 70
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			bike: 30
			htnk: 40
			htnk.ion: 40
			htnk.hover: 40
			htnk.drone: 40
			v3rl: 3
			thwk: 3
			zeus: 3
			titn: 40
			titn.rail: 40
			jugg: 10
			cryo: 10
			mgg: 3
			mrj: 3
			cdrn: 3
			nhaw: 3
			ctnk: 3
			chpr: 3
			batf.ai: 10
			wtnk: 3
			ttnk: 25
			ttra: 25
			isu: 20
			stnk.nod: 15
			hstk: 15
			ftnk: 15
			hftk: 15
			corr: 15
			lchr: 15
			stcr: 25
			oblt: 3
			null: 3
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 1
			msub: 1
			dd: 1
			ca: 1
			cv: 1
			pt: 1
			pt2: 1
			dd2: 1
			ss2: 2
			isub: 1
			sb: 1
			seas: 1
			stmr: 5
			torm: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
		UnitLimits:
			e2: 5
			n2: 8
			e4: 5
			n4: 5
			dog: 2
			cmsr: 1
			mgg: 1
			mrj: 1
			cdrn: 1
			nhaw: 1
			apc.ai: 2
			apc2.nodai: 2
			apc2.gdiai: 2
			rapc.ai: 2
			vulc.ai: 5
			btr.ai: 5
			btr.yuri.ai: 5
			hmmv: 2
			bggy: 2
			jeep: 2
			sapc.ai: 1
			sapc.ai2: 1
			e6: 1
			n6: 1
			s6: 1
			u3.squad: 2
			seal: 5
			mech: 3
			medi: 3
			msub: 4
			ca: 4
			isub: 4
			cv: 4
			jjet: 8
			bjet: 8
			v3rl: 3
			nukc: 3
			thwk: 3
			zeus: 3
			oblt: 3
			kiro: 3
			disc: 3
			deva: 3
			pac: 3
			ss: 10
			ss2: 10
			sb: 10
			seas: 10
			harv: 8
			harv.td: 8
			harv.td.upg: 8
			harv.scrin: 8
			harv.chrono: 8
	SquadManagerBotModuleCA@normal:
		RequiresCondition: enable-normal-ai
		MinimumAttackForceDelay: 25
		SquadValue: 5000
		SquadValueRandomBonus: 2100
		SquadSize: 18
		SquadSizeRandomBonus: 8
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,pt2,ss2,dd2,isub,sb,seas
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		AirToAirPriority: 40
		AirSquadTargetArmorTypes:
			mshp: Wood
	UnitBuilderBotModuleCA@normal:
		RequiresCondition: enable-normal-ai
		IdleBaseUnitsMaximum: 50
		MaxAircraft: 4
		UnitDelays:
			arty: 5250
			arty.nod: 5250
			katy: 5250
		UnitIntervals:
			mshp: 7500
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
			rmbo: 3750
			e7: 3750
			mast: 3750
			yuri: 3750
			bori: 3750
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e6: 5
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n6: 5
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			avtr: 15
			mort.chem: 15
			medi: 3
			mech: 3
			dog: 2
			shok: 15
			ttrp: 15
			brut: 15
			e8: 15
			deso: 15
			snip: 15
			jjet: 15
			bjet: 15
			acol: 15
			tplr: 15
			bh: 15
			ztrp: 15
			zrai: 15
			zdef: 15
			enfo: 15
			hopl: 15
			tigr: 15
			cryt: 15
			evis: 15
			impl: 15
			stlk: 15
			ivan: 15
			cmsr: 15
			rmbo: 15
			e7: 15
			seal: 15
			bori: 15
			yuri: 15
			mast: 15
			apc: 20
			rapc: 20
			sapc: 10
			intl: 10
			jeep: 30
			apc2: 20
			vulc: 10
			hmmv: 30
			gdrn: 30
			mdrn: 20
			xo: 20
			wolv: 20
			pbul: 20
			jack: 10
			btr: 20
			btr.yuri: 20
			trpc: 10
			gunw: 20
			shrw: 20
			bggy: 30
			arty: 20
			howi: 20
			ptnk: 20
			pcan: 20
			v2rl: 20
			katy: 20
			grad: 20
			nukc: 20
			msam: 25
			mlrs: 20
			spec: 20
			hsam: 25
			ruin: 20
			atmz: 20
			1tnk: 70
			ifv.ai: 70
			2tnk: 45
			gtnk.squad: 20
			tnkd: 45
			rtnk: 45
			3tnk: 70
			3tnk.atomic: 70
			3tnk.yuri: 70
			3tnk.atomicyuri: 70
			3tnk.rhino: 70
			3tnk.rhino.atomic: 70
			3tnk.rhino.yuri: 70
			3tnk.rhino.atomicyuri: 70
			4tnk: 40
			4tnk.atomic: 40
			4tnk.erad: 40
			4tnk.erad.atomic: 40
			apoc: 30
			apoc.atomic: 30
			apoc.erad: 30
			apoc.erad.atomic: 30
			ovld: 30
			ovld.atomic: 30
			ovld.erad: 30
			ovld.erad.atomic: 30
			tpod: 40
			rtpd: 40
			ltnk: 40
			ltnk.laser: 40
			mtnk: 70
			mtnk.drone: 70
			mtnk.laser: 70
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			bike: 30
			htnk: 40
			htnk.ion: 40
			htnk.hover: 40
			htnk.drone: 40
			v3rl: 3
			thwk: 3
			zeus: 3
			titn: 40
			titn.rail: 40
			jugg: 10
			cryo: 10
			mgg: 3
			mrj: 3
			cdrn: 3
			nhaw: 3
			ctnk: 3
			chpr: 3
			batf.ai: 10
			wtnk: 3
			ttnk: 25
			ttra: 25
			isu: 20
			stnk.nod: 15
			hstk: 15
			ftnk: 15
			hftk: 15
			corr: 15
			lchr: 15
			stcr: 25
			oblt: 3
			null: 3
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 1
			msub: 1
			dd: 1
			ca: 1
			cv: 1
			pt: 1
			pt2: 1
			dd2: 1
			ss2: 2
			isub: 1
			sb: 1
			seas: 1
			stmr: 5
			torm: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
		UnitLimits:
			e2: 5
			n2: 8
			e4: 5
			n4: 5
			dog: 2
			cmsr: 1
			mgg: 1
			mrj: 1
			cdrn: 1
			nhaw: 1
			apc: 2
			apc2: 2
			rapc: 2
			vulc: 5
			hmmv: 2
			bggy: 2
			jeep: 2
			sapc: 1
			e6: 1
			n6: 1
			s6: 1
			u3.squad: 2
			seal: 5
			mech: 3
			medi: 3
			msub: 4
			ca: 4
			isub: 4
			cv: 4
			jjet: 8
			bjet: 8
			v3rl: 2
			nukc: 2
			thwk: 2
			zeus: 2
			oblt: 2
			kiro: 2
			disc: 2
			deva: 2
			pac: 2
			ss: 10
			ss2: 10
			sb: 10
			seas: 10
			harv: 8
			harv.td: 8
			harv.td.upg: 8
			harv.scrin: 8
			harv.chrono: 8
	SquadManagerBotModuleCA@easy:
		RequiresCondition: enable-easy-ai
		SquadValue: 4000
		SquadValueRandomBonus: 1500
		MinimumAttackForceDelay: 25
		SquadSize: 14
		SquadSizeRandomBonus: 5
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,pt2,ss2,dd2,isub,sb,seas
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		AirToAirPriority: 30
		AirSquadTargetArmorTypes:
			mshp: Wood
	UnitBuilderBotModuleCA@easy:
		RequiresCondition: enable-easy-ai
		MaxAircraft: 2
		UnitDelays:
			arty: 6750
			arty.nod: 6750
			katy: 6750
			msam: 6750
		UnitIntervals:
			mshp: 7500
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
			rmbo: 4500
			e7: 4500
			mast: 4500
			yuri: 4500
			bori: 4500
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			e6: 5
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n6: 5
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			avtr: 15
			mort.chem: 15
			medi: 3
			mech: 3
			dog: 2
			shok: 15
			ttrp: 15
			brut: 15
			e8: 15
			deso: 15
			snip: 15
			jjet: 15
			bjet: 15
			acol: 15
			tplr: 15
			bh: 15
			ztrp: 15
			zrai: 15
			zdef: 15
			enfo: 15
			hopl: 15
			tigr: 15
			cryt: 15
			evis: 15
			impl: 15
			stlk: 15
			ivan: 15
			cmsr: 15
			rmbo: 15
			e7: 15
			seal: 15
			bori: 15
			yuri: 15
			mast: 15
			apc: 20
			rapc: 20
			sapc: 10
			intl: 10
			jeep: 30
			apc2: 20
			vulc: 10
			hmmv: 30
			gdrn: 30
			mdrn: 20
			xo: 20
			wolv: 20
			pbul: 20
			jack: 10
			btr: 20
			btr.yuri: 20
			trpc: 10
			gunw: 20
			shrw: 20
			bggy: 30
			arty: 20
			howi: 20
			ptnk: 20
			pcan: 20
			v2rl: 20
			katy: 20
			grad: 20
			nukc: 20
			msam: 25
			mlrs: 20
			spec: 20
			hsam: 25
			ruin: 20
			atmz: 20
			1tnk: 70
			ifv.ai: 70
			2tnk: 45
			gtnk.squad: 20
			tnkd: 45
			rtnk: 45
			3tnk: 70
			3tnk.atomic: 70
			3tnk.yuri: 70
			3tnk.atomicyuri: 70
			3tnk.rhino: 70
			3tnk.rhino.atomic: 70
			3tnk.rhino.yuri: 70
			3tnk.rhino.atomicyuri: 70
			4tnk: 40
			4tnk.atomic: 40
			4tnk.erad: 40
			4tnk.erad.atomic: 40
			apoc: 30
			apoc.atomic: 30
			apoc.erad: 30
			apoc.erad.atomic: 30
			ovld: 30
			ovld.atomic: 30
			ovld.erad: 30
			ovld.erad.atomic: 30
			tpod: 40
			rtpd: 40
			ltnk: 40
			ltnk.laser: 40
			mtnk: 70
			mtnk.drone: 70
			mtnk.laser: 70
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			bike: 30
			htnk: 40
			htnk.ion: 40
			htnk.hover: 40
			htnk.drone: 40
			v3rl: 3
			thwk: 3
			zeus: 3
			titn: 40
			titn.rail: 40
			jugg: 10
			cryo: 10
			mgg: 3
			mrj: 3
			cdrn: 3
			nhaw: 3
			ctnk: 3
			chpr: 3
			batf.ai: 10
			wtnk: 3
			ttnk: 25
			ttra: 25
			isu: 20
			stnk.nod: 15
			hstk: 15
			ftnk: 15
			hftk: 15
			corr: 15
			lchr: 15
			stcr: 25
			oblt: 3
			null: 3
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 1
			msub: 1
			dd: 1
			ca: 1
			cv: 1
			pt: 1
			pt2: 1
			dd2: 1
			ss2: 2
			isub: 1
			sb: 1
			seas: 1
			stmr: 5
			torm: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
		UnitLimits:
			e2: 5
			n2: 8
			e4: 5
			n4: 5
			dog: 2
			cmsr: 1
			mgg: 1
			mrj: 1
			cdrn: 1
			nhaw: 1
			apc: 2
			apc2: 2
			rapc: 2
			vulc: 5
			hmmv: 2
			bggy: 2
			jeep: 2
			sapc: 1
			e6: 1
			n6: 1
			s6: 1
			u3.squad: 2
			seal: 5
			mech: 3
			medi: 3
			msub: 4
			ca: 4
			isub: 4
			cv: 4
			jjet: 8
			bjet: 8
			v3rl: 1
			nukc: 1
			thwk: 1
			zeus: 1
			oblt: 1
			kiro: 1
			disc: 1
			deva: 1
			pac: 1
			ss: 10
			ss2: 10
			sb: 10
			seas: 10
			harv: 8
			harv.td: 8
			harv.td.upg: 8
			harv.scrin: 8
			harv.chrono: 8
	SquadManagerBotModuleCA@naval:
		RequiresCondition: enable-naval-ai
		MinimumAttackForceDelay: 25
		SquadSize: 1
		AirUnitsTypes: heli, harr, pmak, beag, hind, yak, mig, suk, suk.upg, kiro, orca, a10, a10.sw, a10.gau, orcb, auro, jack, apch, venm, rah, scrn, stmr, torm, enrv, mshp, phan, kamv, shde, vert, mcor
		ExcludeFromSquadsTypes: harv, harv.td, harv.td.upg, harv.scrin, harv.chrono, mcv, amcv, smcv, dog, e6, n6, s6, badr, badr.bomber, badr.cbomber, badr.nbomber, badr.mbomber, b2b, p51, tran.paradrop, halo.paradrop, nhaw.paradrop, u2, smig, a10.bomber, c17, c17.cargo, c17.clustermines, c17.xo, galx, uav, ocar.reinforce, ocar.xo, ocar.pod, horn, yf23.bomber, pod, pod2, pod3, buzz, buzz.ai, mspk
		NavalUnitsTypes: ss,msub,dd,ca,lst,pt,pt2,dd2,ss2,isub,sb,seas
		ConstructionYardTypes: fact,afac,sfac
		StaticAntiAirTypes: agun, sam, nsam, cram, shar
		NavalProductionTypes: spen,syrd,spen.nod,syrd.gdi
		AirSquadTargetArmorTypes:
			mshp: Wood
	UnitBuilderBotModuleCA@naval:
		RequiresCondition: enable-naval-ai
		MaxAircraft: 4
		UnitIntervals:
			mshp: 7500
			harv: 2250
			harv.td: 2250
			harv.td.upg: 2250
			harv.scrin: 2250
			harv.chrono: 2250
		UnitsToBuild:
			e1: 65
			e2: 25
			e3: 40
			e4: 15
			n1: 65
			n2: 25
			n3: 40
			n4: 15
			n5: 15
			n1c: 65
			n3c: 40
			s1: 65
			s2: 40
			s3: 40
			s4: 15
			s6: 5
			u3.squad: 40
			rmbc: 15
			enli: 10
			reap: 10
			dog: 2
			jjet: 15
			bjet: 15
			seek: 40
			lace: 40
			devo: 45
			dark: 45
			hsam: 40
			hind: 5
			heli: 5
			apch: 5
			venm: 5
			orca: 5
			orcb: 3
			scrn: 5
			rah: 3
			mig: 3
			suk: 3
			suk.upg: 3
			harr: 3
			pmak: 3
			beag: 3
			yak: 2
			disr: 10
			kiro: 2
			disc: 2
			a10: 5
			a10.sw: 5
			a10.gau: 5
			auro: 5
			ss: 20
			msub: 10
			dd: 10
			ca: 10
			cv: 10
			pt: 20
			pt2: 20
			dd2: 10
			ss2: 20
			isub: 10
			sb: 10
			seas: 10
			stmr: 5
			torm: 5
			enrv: 3
			deva: 3
			pac: 3
			mshp: 1
