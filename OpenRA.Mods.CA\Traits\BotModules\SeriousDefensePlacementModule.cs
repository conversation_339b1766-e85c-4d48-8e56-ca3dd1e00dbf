#region Copyright & License Information
/*
 * Copyright 2007-2022 The OpenRA Developers (see AUTHORS)
 * This file is part of OpenRA, which is free software. It is made
 * available to you under the terms of the GNU General Public License
 * as published by the Free Software Foundation, either version 3 of
 * the License, or (at your option) any later version. For more
 * information, see COPYING.
 */
#endregion

using System;
using System.Collections.Generic;
using System.Linq;
using OpenRA.Mods.Common;
using OpenRA.Mods.Common.Traits;
using OpenRA.Traits;

namespace OpenRA.Mods.CA.Traits
{
	[Desc("Serious AI defense placement module - places defense towers intelligently next to walls and away from map edges.")]
	public class SeriousDefensePlacementModuleInfo : ConditionalTraitInfo
	{
		[Desc("Actor types that are considered defense towers.")]
		public readonly HashSet<string> DefenseTypes = new HashSet<string> { "pbox", "gun", "atwr", "sam", "agun", "obli", "gtwr", "nuke", "tmpl", "ftur", "ptur", "ltur" };

		[Desc("Minimum distance from map edge to place defense towers.")]
		public readonly int MinDistanceFromMapEdge = 5;

		[Desc("Maximum distance from walls to place defense towers.")]
		public readonly int MaxDistanceFromWalls = 3;

		[Desc("Interval between defense placement checks (in ticks).")]
		public readonly int CheckInterval = 250;

		public override object Create(ActorInitializer init) { return new SeriousDefensePlacementModule(init.Self, this); }
	}

	public class SeriousDefensePlacementModule : ConditionalTrait<SeriousDefensePlacementModuleInfo>, IBotTick, IBotPositionsUpdated
	{
		readonly World world;
		readonly Player player;
		readonly SeriousDefensePlacementModuleInfo info;

		int ticksBeforeNextCheck;
		CPos baseCenter;
		bool isActive;

		public SeriousDefensePlacementModule(Actor self, SeriousDefensePlacementModuleInfo info)
			: base(info)
		{
			this.info = info;
			world = self.World;
			player = self.Owner;
			ticksBeforeNextCheck = info.CheckInterval;

			// We'll check if this is the Serious AI later when PlayerActor is available
			isActive = false;
		}

		void IBotPositionsUpdated.UpdatedBaseCenter(CPos newLocation)
		{
			baseCenter = newLocation;
		}

		void IBotPositionsUpdated.UpdatedDefenseCenter(CPos newLocation) { }

		void IBotTick.BotTick(IBot bot)
		{
			if (IsTraitDisabled)
				return;

			// Check if this is the Serious AI on first tick
			if (!isActive)
			{
				// Check if this is the Serious AI by looking for the SeriousWallBuilderModule
				if (player.PlayerActor != null)
				{
					isActive = player.PlayerActor.TraitsImplementing<object>()
						.Any(t => t.GetType().Name == "SeriousWallBuilderModule");

					if (!isActive)
						return; // Not Serious AI, disable this module
				}
				else
				{
					return; // PlayerActor not ready yet
				}
			}

			if (--ticksBeforeNextCheck <= 0)
			{
				ticksBeforeNextCheck = info.CheckInterval;

				// Check if we should override defense placement
				CheckAndOverrideDefensePlacement(bot);
			}
		}

		void CheckAndOverrideDefensePlacement(IBot bot)
		{
			// The new grid-based placement system in BaseBuilderQueueManagerCA now handles
			// defense placement intelligently, so this module is no longer needed to override placement.
			// We'll keep this method for potential future enhancements but disable the override logic.

			AIUtils.BotDebug("Serious AI: Defense Placement: Grid-based system is handling defense placement");
			return;
		}

		CPos? FindIntelligentDefenseLocation(string defenseType)
		{
			// Get all existing walls
			var existingWalls = world.ActorsHavingTrait<Building>()
				.Where(a => a.Owner == player && IsWallType(a.Info.Name))
				.Select(a => a.Location)
				.ToHashSet();

			if (existingWalls.Count == 0)
			{
				AIUtils.BotDebug("Serious AI: Defense Placement: No walls found, cannot place defense intelligently");
				return null;
			}

			// Get actor info for the defense building
			var actorInfo = world.Map.Rules.Actors[defenseType];
			var buildingInfo = actorInfo.TraitInfo<BuildingInfo>();

			// Find positions adjacent to walls
			var candidatePositions = new List<CPos>();
			
			foreach (var wallPos in existingWalls)
			{
				// Check all adjacent positions to this wall
				foreach (var dir in CVec.Directions)
				{
					var candidatePos = wallPos + dir;
					
					// Skip if position is not suitable
					if (!IsPositionSuitableForDefense(candidatePos, actorInfo, buildingInfo))
						continue;
					
					candidatePositions.Add(candidatePos);
				}
			}

			if (candidatePositions.Count == 0)
			{
				AIUtils.BotDebug("Serious AI: Defense Placement: No suitable positions found adjacent to walls");
				return null;
			}

			// Filter positions based on map edge distance and enemy direction
			var filteredPositions = FilterPositionsByMapEdgeAndEnemyDirection(candidatePositions);
			
			if (filteredPositions.Count == 0)
			{
				AIUtils.BotDebug("Serious AI: Defense Placement: No positions remain after filtering");
				return filteredPositions.FirstOrDefault();
			}

			// Return the best position (closest to base center)
			var bestPosition = filteredPositions
				.OrderBy(pos => (pos - baseCenter).LengthSquared)
				.FirstOrDefault();

			return bestPosition;
		}

		bool IsWallType(string actorName)
		{
			// Common wall types in Combined Arms
			var wallTypes = new HashSet<string> { "sbag", "fenc", "chain", "brik", "swal", "barb" };
			return wallTypes.Contains(actorName.ToLowerInvariant());
		}

		bool IsPositionSuitableForDefense(CPos position, ActorInfo actorInfo, BuildingInfo buildingInfo)
		{
			// Check if position is within map bounds
			if (!world.Map.Contains(position))
				return false;

			// Check if building can be placed here
			if (!world.CanPlaceBuilding(position, actorInfo, buildingInfo, null))
				return false;

			// Check if position is close enough to base
			if (!buildingInfo.IsCloseEnoughToBase(world, player, actorInfo, position))
				return false;

			return true;
		}

		List<CPos> FilterPositionsByMapEdgeAndEnemyDirection(List<CPos> positions)
		{
			var mapBounds = world.Map.Bounds;
			var filteredPositions = new List<CPos>();

			// Determine which sides of the map are "safe" (towards map edges)
			// and which sides face potential enemies
			var baseX = baseCenter.X;
			var baseY = baseCenter.Y;
			var mapCenterX = mapBounds.Left + mapBounds.Width / 2;
			var mapCenterY = mapBounds.Top + mapBounds.Height / 2;

			foreach (var pos in positions)
			{
				// Check minimum distance from map edges
				var distanceFromLeft = pos.X - mapBounds.Left;
				var distanceFromRight = mapBounds.Right - pos.X;
				var distanceFromTop = pos.Y - mapBounds.Top;
				var distanceFromBottom = mapBounds.Bottom - pos.Y;

				var minDistanceFromEdge = Math.Min(Math.Min(distanceFromLeft, distanceFromRight),
					Math.Min(distanceFromTop, distanceFromBottom));

				if (minDistanceFromEdge < info.MinDistanceFromMapEdge)
					continue;

				// Determine if this position faces towards the center of the map (where enemies likely are)
				// Only place defenses on sides that face towards the map center
				bool facesTowardsCenter = false;

				// If base is in bottom-right, only place defenses on top and left sides
				if (baseX > mapCenterX && baseY > mapCenterY)
				{
					facesTowardsCenter = (pos.X <= baseX) || (pos.Y <= baseY);
				}
				// If base is in bottom-left, only place defenses on top and right sides
				else if (baseX <= mapCenterX && baseY > mapCenterY)
				{
					facesTowardsCenter = (pos.X >= baseX) || (pos.Y <= baseY);
				}
				// If base is in top-right, only place defenses on bottom and left sides
				else if (baseX > mapCenterX && baseY <= mapCenterY)
				{
					facesTowardsCenter = (pos.X <= baseX) || (pos.Y >= baseY);
				}
				// If base is in top-left, only place defenses on bottom and right sides
				else
				{
					facesTowardsCenter = (pos.X >= baseX) || (pos.Y >= baseY);
				}

				if (facesTowardsCenter)
				{
					filteredPositions.Add(pos);
				}
			}

			AIUtils.BotDebug("Serious AI: Defense Placement: Filtered {0} positions to {1} positions facing towards map center",
				positions.Count, filteredPositions.Count);

			return filteredPositions;
		}
	}
}
